class FlowFreeSolver:
    def __init__(self, rows, cols, pairs):
        """
        Initialize the Flow Free solver.

        Args:
            rows (int): Number of rows in the grid
            cols (int): Number of columns in the grid
            pairs (dict): Dictionary where key is the symbol and value is tuple of two coordinates
                         Example: {1: ((0,0), (3,5)), 2: ((2,3), (6,2))}
        """
        self.rows = rows
        self.cols = cols
        self.pairs = pairs
        self.grid = [[0 for _ in range(cols)] for _ in range(rows)]
        self.paths = {}  # Store the current path for each pair
        self.all_paths = {}  # Store all valid paths for each symbol with quality metrics

        # Debug flags for bad shape detection
        self.debug = {
            'check_2x2_square': True,
            'check_isolated_cell': True,
            'check_trapped_endpoint': True,
            'check_c_shape': True
        }

        # Mark the endpoints in the grid
        for symbol, (start, end) in pairs.items():
            self.grid[start[0]][start[1]] = symbol
            self.grid[end[0]][end[1]] = symbol
            self.all_paths[symbol] = []  # Initialize empty list for each symbol's paths

    def get_neighbors(self, row, col):
        """Get valid neighboring cells (cardinal directions only)."""
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]  # right, down, left, up
        neighbors = []

        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < self.rows and 0 <= new_col < self.cols:
                neighbors.append((new_row, new_col))

        return neighbors

    def get_ordered_neighbors(self, row, col, symbol, path):
        neighbors = self.get_neighbors(row, col)
        target_end = self.pairs[symbol][1]
        if len(path) == 1:
            required_move = self.get_required_first_move(symbol)
            if required_move and required_move in neighbors:
                return [required_move]
        ordered = []
        if target_end in neighbors:
            ordered.append(target_end)
            neighbors.remove(target_end)
        good_shape_neighbors = []
        edge_neighbors = []
        regular_neighbors = []
        for nr, nc in neighbors:
            # Skip neighbors that would create bad shapes
            temp_path = path + [(nr, nc)]
            if self.has_bad_shape(temp_path, symbol):
                continue
            
            if self.creates_good_shape(nr, nc, symbol):
                good_shape_neighbors.append((nr, nc))
            elif self.is_edge(nr, nc):
                edge_neighbors.append((nr, nc))
            else:
                regular_neighbors.append((nr, nc))
        ordered.extend(good_shape_neighbors)
        ordered.extend(edge_neighbors)
        ordered.extend(regular_neighbors)
        return ordered

    def get_required_first_move(self, symbol):
        """
        Get the required first move for a symbol based on heuristic rules.
        Returns the required next position or None
        """
        start, _ = self.pairs[symbol]
        if self.is_edge(start[0], start[1]):
            is_near, corner = self.is_near_corner(start[0], start[1])
            if is_near:
                return corner
        return None

    def would_have_eight_empty_neighbors(self, center_row, center_col, new_row, new_col, symbol):
        """
        Check if a center position would have 8 empty neighbors if we place symbol at (new_row, new_col).
        This checks all 8 directions around the center (including diagonals).
        """
        # All 8 directions: N, NE, E, SE, S, SW, W, NW
        directions = [(-1, 0), (-1, 1), (0, 1), (1, 1), (1, 0), (1, -1), (0, -1), (-1, -1)]

        empty_count = 0
        for dr, dc in directions:
            check_row, check_col = center_row + dr, center_col + dc

            # Skip if out of bounds
            if not (0 <= check_row < self.rows and 0 <= check_col < self.cols):
                continue

            # Check what would be at this position
            if (check_row, check_col) == (new_row, new_col):
                continue  # This would be filled by our new move, so not empty
            elif self.grid[check_row][check_col] == 0:
                empty_count += 1

        # Return True if we'd have many empty neighbors (6+ is good, 8 is ideal but rare)
        return empty_count >= 6

    def creates_good_shape(self, row, col, symbol):
        """
        Check if moving to this position creates a good shape.
        A good shape is when a node (part of current path) has 8 empty neighbors around it.
        This creates flexible space for other paths to navigate around.
        """
        # First, check if placing our symbol here would create the good pattern
        # We need to temporarily place the symbol and check if any existing path nodes
        # now have 8 empty neighbors

        # Get all positions that will be part of our path if we make this move
        current_path_positions = set()
        for other_symbol, other_path in self.paths.items():
            if other_symbol != symbol:
                current_path_positions.update(other_path[1:-1])  # Exclude endpoints

        # Check if making this move would create a node with 8 empty neighbors
        # Look at all current path positions and see if any would benefit
        for path_row, path_col in current_path_positions:
            if self.would_have_eight_empty_neighbors(path_row, path_col, row, col, symbol):
                return True

        # Also check if the new position itself might create a good central hub
        # (though this is less common since we're building the path)
        return False

    def is_corner(self, row, col):
        """Check if position is a corner of the grid."""
        return ((row == 0 or row == self.rows - 1) and
                (col == 0 or col == self.cols - 1))

    def is_edge(self, row, col):
        """Check if position is on the edge of the grid."""
        return (row == 0 or row == self.rows - 1 or
                col == 0 or col == self.cols - 1)

    def is_near_corner(self, row, col):
        """Check if position is one space away from a corner."""
        corners = [(0, 0), (0, self.cols - 1), (self.rows - 1, 0), (self.rows - 1, self.cols - 1)]
        for corner_row, corner_col in corners:
            if (abs(row - corner_row) == 1 and col == corner_col) or \
                    (abs(col - corner_col) == 1 and row == corner_row):
                return True, (corner_row, corner_col)
        return False, None

    def is_valid_move(self, row, col, symbol):
        """Check if a move to (row, col) is valid for the given symbol."""
        # Check bounds
        if row < 0 or row >= self.rows or col < 0 or col >= self.cols:
            return False

        # Check if cell is empty or is the target endpoint
        target_end = self.pairs[symbol][1]
        if self.grid[row][col] == 0 or (row, col) == target_end:
            return True

        return False

    def is_edge_preferred_move(self, current_pos, next_pos):
        """
        Check if a move from current_pos to next_pos follows edge-preferred routing.
        Prioritizes staying on edges when possible.
        """
        current_row, current_col = current_pos
        next_row, next_col = next_pos

        # If we're currently on an edge, prefer to stay on edges
        if self.is_edge(current_row, current_col):
            return self.is_edge(next_row, next_col)

        # If we're not on an edge, moving to an edge is still good
        return self.is_edge(next_row, next_col)

    def has_bad_shape(self, path, symbol):
        """
        Check if the current path contains any "bad shapes" that make the puzzle unsolvable.
        Returns True if a bad shape is detected.
        """
        if len(path) < 3:
            return False

        # Get the last position
        current = path[-1]

        print(f"  🔍 Checking for bad shapes at {current} for path of length {len(path)}")

        # Bad Shape 1: Creating a 2x2 square (very common bad pattern)
        if self.debug['check_2x2_square'] and len(path) >= 4:
            square_result = self.creates_2x2_square(path[-4:])
            if square_result:
                print(f"  ❌ Bad shape detected: 2x2 square at {path[-4:]}")
                return True

        # Bad Shape 2: Creating isolated single cells
        if self.debug['check_isolated_cell']:
            isolated_result = self.creates_isolated_cell(current, symbol)
            if isolated_result:
                print(f"  ❌ Bad shape detected: isolated cell near {current}")
                return True

        # Bad Shape 3: Trapping other endpoints
        if self.debug['check_trapped_endpoint']:
            trapped_result = self.traps_endpoint(current, symbol)
            if trapped_result:
                print(f"  ❌ Bad shape detected: trapping endpoint at {current}")
                return True

        # Bad Shape 4: C-shapes and concave patterns
        if self.debug['check_c_shape'] and len(path) >= 5:
            # Check for partial C-shapes (5 points)
            c_shape_result = self.creates_c_shape(path[-5:])
            if c_shape_result:
                print(f"  ❌ Bad shape detected: C-shape pattern at {path[-5:]}")
                return True

            # Check for complete C-shapes (7+ points) if path is long enough
            if len(path) >= 7:
                complete_c_result = self.creates_c_shape(path[-7:])
                if complete_c_result:
                    print(f"  ❌ Bad shape detected: Complete C-shape pattern at {path[-7:]}")
                    return True

        print(f"  ✅ No bad shapes detected at {current}")
        return False

    def creates_2x2_square(self, recent_path):
        """Check if recent path creates a 2x2 square pattern."""
        if len(recent_path) < 4:
            return False

        # Get the 4 most recent positions
        positions = recent_path[-4:]
        print(f"    🔍 2x2 square check: Examining points {positions}")

        # Check if they form a 2x2 square
        rows = [pos[0] for pos in positions]
        cols = [pos[1] for pos in positions]

        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)

        # If they span exactly 2 rows and 2 columns, it might be a 2x2 square
        if max_row - min_row == 1 and max_col - min_col == 1:
            expected_square = {(min_row, min_col), (min_row, max_col),
                               (max_row, min_col), (max_row, max_col)}
            is_square = set(positions[-4:]) == expected_square
            if is_square:
                print(f"    ❌ Detected 2x2 square: {positions}")
            return is_square

        return False

    def creates_isolated_cell(self, current_pos, symbol):
        """Check if current position creates an isolated cell that can't be filled."""
        row, col = current_pos

        # Get the target endpoint for this symbol
        target_end = self.pairs[symbol][1]

        # Check all neighbors of the current position
        for dr, dc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
            neighbor_row, neighbor_col = row + dr, col + dc

            # Skip if out of bounds or not empty
            if not (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols):
                continue
            if self.grid[neighbor_row][neighbor_col] != 0:
                continue

            # Count empty neighbors of this neighbor
            empty_count = 0
            for ndr, ndc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
                check_row, check_col = neighbor_row + ndr, neighbor_col + ndc
                if 0 <= check_row < self.rows and 0 <= check_col < self.cols:
                    if (check_row, check_col) == current_pos:
                        continue  # Skip the current position
                    if self.grid[check_row][check_col] == 0:
                        empty_count += 1

            # Only consider it isolated if it has NO empty neighbors (completely surrounded)
            # Having 1 empty neighbor is fine - the path can reach it from that direction
            if empty_count == 0:
                is_endpoint = False
                is_current_target = False

                # Check if this cell is an endpoint of any unsolved pair
                for other_symbol, (start, end) in self.pairs.items():
                    if other_symbol not in self.paths:  # Only check unsolved pairs
                        if (neighbor_row, neighbor_col) == start or (neighbor_row, neighbor_col) == end:
                            is_endpoint = True
                            break

                # Check if this cell is on the path to our current target
                # If this cell is adjacent to our target endpoint, it might be part of our path
                if (neighbor_row, neighbor_col) != target_end:
                    target_row, target_col = target_end
                    distance_to_target = abs(neighbor_row - target_row) + abs(neighbor_col - target_col)
                    if distance_to_target == 1:  # Adjacent to our target
                        is_current_target = True
                        print(f"    ℹ️  Cell ({neighbor_row}, {neighbor_col}) is adjacent to our target {target_end}, might be part of our path")

                # If not an endpoint and not potentially part of our path, it would be completely isolated
                if not is_endpoint and not is_current_target:
                    print(f"    ❌ Would create completely isolated cell at ({neighbor_row}, {neighbor_col})")
                    return True

        return False



    def traps_endpoint(self, current_pos, current_symbol):
        """Check if current position traps an endpoint of another unsolved pair."""
        # For each unsolved pair, check if we're blocking access to its endpoints
        for symbol, (start, end) in self.pairs.items():
            if symbol == current_symbol or symbol in self.paths:
                continue  # Skip current symbol and already solved pairs

            # Check if we're trapping either endpoint
            for endpoint in [start, end]:
                # Don't flag as trapped if the endpoint is adjacent to its connected endpoint
                # (they can still connect directly)
                other_endpoint = end if endpoint == start else start

                # Check if the endpoint is adjacent to its connected endpoint
                endpoint_row, endpoint_col = endpoint
                other_row, other_col = other_endpoint
                is_adjacent_to_connected = (abs(endpoint_row - other_row) +
                                          abs(endpoint_col - other_col) == 1)

                # Only check for trapping if the endpoint is NOT adjacent to its connected endpoint
                if not is_adjacent_to_connected and self.would_trap_position(endpoint, current_pos):
                    print(f"    ❌ Would trap endpoint {endpoint} of symbol {symbol}")
                    return True

        return False

    def would_trap_position(self, target_pos, blocking_pos):
        """Check if placing something at blocking_pos would trap target_pos."""
        target_row, target_col = target_pos

        # Count accessible neighbors from target position
        accessible_neighbors = 0
        for dr, dc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
            new_row, new_col = target_row + dr, target_col + dc
            if 0 <= new_row < self.rows and 0 <= new_col < self.cols:
                if (new_row, new_col) == blocking_pos:
                    continue  # This would be blocked
                if self.grid[new_row][new_col] == 0:
                    accessible_neighbors += 1

        # Only consider it trapped if it would have NO accessible neighbors (completely surrounded)
        # Having 1 accessible neighbor is still okay - the path can come from that direction
        trapped = accessible_neighbors == 0
        if trapped:
            print(f"    ⚠️  Position {target_pos} would be completely trapped with {accessible_neighbors} accessible neighbors")
        return trapped

    def creates_c_shape(self, recent_path):
        """Check if the recent path creates a C-shape or similar concave pattern."""
        if len(recent_path) < 5:
            return False

        print(f"    🔍 C-shape check: Examining {len(recent_path)} points: {recent_path}")

        # Extract coordinates
        coords = [(pos[0], pos[1]) for pos in recent_path]

        # Check if we have a C-shape pattern
        if self._is_c_shape_pattern(coords):
            print(f"    ❌ Detected C-shape pattern: {coords}")
            return True

        # Check for patterns that create pockets/bays with trapped cells
        if self._creates_pocket_pattern(recent_path):
            print(f"    ❌ Detected pocket/bay pattern in path")
            return True

        # Check for extended patterns with multiple consecutive trapped cells
        if self._check_for_extended_c_patterns(recent_path):
            print(f"    ❌ Detected extended C-pattern with multiple trapped cells")
            return True

        return False

    def _is_c_shape_pattern(self, coords):
        """
        Check if coordinates form a C-shape pattern.
        A true C-shape requires at least 7 points to form the complete pattern:
        - Initial segment (2+ points)
        - Turn segment (1 point)
        - Middle segment (2+ points)
        - Turn segment (1 point)
        - Final segment (2+ points going back toward initial direction)

        For 5 points, we check for partial C-shapes that would create problematic concave patterns.
        """
        if len(coords) < 5:
            return False

        # For exactly 5 points, check for the start of a C-shape pattern
        if len(coords) == 5:
            return self._is_partial_c_shape(coords)

        # For 7+ points, check for complete C-shape patterns
        if len(coords) >= 7:
            return self._is_complete_c_shape(coords)

        return False

    def _is_partial_c_shape(self, coords):
        """Check if 5 coordinates form the beginning of a problematic C-shape pattern."""
        if len(coords) != 5:
            return False

        # Pattern 1: Horizontal line followed by vertical turn (┐ or ┌ shape start)
        # Check if first 3 points are horizontal
        if (coords[0][0] == coords[1][0] == coords[2][0] and
            abs(coords[1][1] - coords[0][1]) == 1 and
            abs(coords[2][1] - coords[1][1]) == 1):

            # Check if next 2 points go vertical (same direction)
            if (coords[2][1] == coords[3][1] == coords[4][1] and
                abs(coords[3][0] - coords[2][0]) == 1 and
                abs(coords[4][0] - coords[3][0]) == 1 and
                (coords[4][0] - coords[3][0]) == (coords[3][0] - coords[2][0])):  # Same direction

                # This creates a concave pattern that could trap cells
                return True

        # Pattern 2: Vertical line followed by horizontal turn (└ or ┘ shape start)
        # Check if first 3 points are vertical
        if (coords[0][1] == coords[1][1] == coords[2][1] and
            abs(coords[1][0] - coords[0][0]) == 1 and
            abs(coords[2][0] - coords[1][0]) == 1):

            # Check if next 2 points go horizontal (same direction)
            if (coords[2][0] == coords[3][0] == coords[4][0] and
                abs(coords[3][1] - coords[2][1]) == 1 and
                abs(coords[4][1] - coords[3][1]) == 1 and
                (coords[4][1] - coords[3][1]) == (coords[3][1] - coords[2][1])):  # Same direction

                # This creates a concave pattern that could trap cells
                return True

        return False

    def _is_complete_c_shape(self, coords):
        """Check if 7+ coordinates form a complete C-shape pattern."""
        if len(coords) < 7:
            return False

        # Check for complete C-shape: horizontal → vertical → horizontal (opposite direction)
        # Pattern 1: ┌─┐ shape (or ┐─┌ shape)
        #            │ │
        #
        for i in range(len(coords) - 6):
            segment = coords[i:i+7]

            # Check if first 3 points are horizontal
            if (segment[0][0] == segment[1][0] == segment[2][0] and
                abs(segment[1][1] - segment[0][1]) == 1 and
                abs(segment[2][1] - segment[1][1]) == 1):

                # Check if next 2 points go vertical
                if (segment[2][1] == segment[3][1] == segment[4][1] and
                    abs(segment[3][0] - segment[2][0]) == 1 and
                    abs(segment[4][0] - segment[3][0]) == 1):

                    # Check if final 2 points go horizontal in OPPOSITE direction to first segment
                    if (segment[4][0] == segment[5][0] == segment[6][0] and
                        abs(segment[5][1] - segment[4][1]) == 1 and
                        abs(segment[6][1] - segment[5][1]) == 1):

                        # Verify the final horizontal direction is opposite to the initial
                        initial_direction = segment[2][1] - segment[0][1]  # +1 or -1
                        final_direction = segment[6][1] - segment[4][1]    # +1 or -1

                        if initial_direction * final_direction < 0:  # Opposite directions
                            return True

            # Check for vertical → horizontal → vertical C-shape
            # Pattern 2: ┌ ┐ shape
            #            │ │
            #            └ ┘
            if (segment[0][1] == segment[1][1] == segment[2][1] and
                abs(segment[1][0] - segment[0][0]) == 1 and
                abs(segment[2][0] - segment[1][0]) == 1):

                # Check if next 2 points go horizontal
                if (segment[2][0] == segment[3][0] == segment[4][0] and
                    abs(segment[3][1] - segment[2][1]) == 1 and
                    abs(segment[4][1] - segment[3][1]) == 1):

                    # Check if final 2 points go vertical in OPPOSITE direction to first segment
                    if (segment[4][1] == segment[5][1] == segment[6][1] and
                        abs(segment[5][0] - segment[4][0]) == 1 and
                        abs(segment[6][0] - segment[5][0]) == 1):

                        # Verify the final vertical direction is opposite to the initial
                        initial_direction = segment[2][0] - segment[0][0]  # +1 or -1
                        final_direction = segment[6][0] - segment[4][0]    # +1 or -1

                        if initial_direction * final_direction < 0:  # Opposite directions
                            return True

        return False

    def _creates_pocket_pattern(self, path):
        """Check if the path creates a pocket or bay that traps empty cells."""
        if len(path) < 4:
            return False

        # Look for patterns where the path surrounds empty cells
        # This is more general than the specific 5-point C-shape

        # Get the current position (end of path)
        current_pos = path[-1]
        current_row, current_col = current_pos

        # Check if we're creating a concave shape by looking at the area around our path
        # A pocket is formed when we have empty cells that are surrounded on 3+ sides by our path
        # BUT we need to be more careful about what constitutes a "pocket"

        # Look for empty cells near our current position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue

                check_row, check_col = current_row + dr, current_col + dc
                if not (0 <= check_row < self.rows and 0 <= check_col < self.cols):
                    continue

                # If this cell is empty, check if it's being surrounded by our path
                if self.grid[check_row][check_col] == 0:
                    if self._is_cell_truly_pocketed(check_row, check_col, path):
                        print(f"    ❌ Cell ({check_row}, {check_col}) is being pocketed by path")
                        return True

        return False

    def _is_cell_truly_pocketed(self, row, col, path):
        """
        Check if a cell is truly pocketed by the current path.
        A cell is pocketed only if it creates a concave indentation that would be impossible
        to reach by any future path. We need to be very conservative here to avoid false positives.
        """
        path_positions = set(path)

        # Check all 4 directions around the cell
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]  # right, down, left, up
        blocked_sides = []
        open_sides = []

        for i, (dr, dc) in enumerate(directions):
            neighbor_row, neighbor_col = row + dr, col + dc

            # Check if this side is blocked
            is_blocked = False
            if not (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols):
                # Out of bounds - consider as blocked
                is_blocked = True
            elif (neighbor_row, neighbor_col) in path_positions:
                # Part of current path - blocked
                is_blocked = True
            elif (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols and
                  self.grid[neighbor_row][neighbor_col] != 0):
                # Occupied by another symbol - blocked
                is_blocked = True

            if is_blocked:
                blocked_sides.append(i)
            else:
                open_sides.append(i)

        # Be much more conservative about what constitutes a "pocket"
        # Only flag as pocketed if:
        # 1. ALL 4 sides are blocked (completely surrounded), OR
        # 2. 3 sides blocked AND the remaining opening leads to a dead end

        if len(blocked_sides) == 4:
            print(f"    🔍 Cell ({row}, {col}) is completely surrounded - truly pocketed")
            return True

        if len(blocked_sides) == 3 and len(open_sides) == 1:
            # Check if the single opening leads to a dead end
            open_direction = open_sides[0]
            dr, dc = directions[open_direction]
            escape_row, escape_col = row + dr, col + dc

            # If the escape route itself would be surrounded, then this is a pocket
            if self._would_be_dead_end(escape_row, escape_col, path_positions):
                print(f"    🔍 Cell ({row}, {col}) has only one escape route that leads to dead end - truly pocketed")
                return True
            else:
                print(f"    ✅ Cell ({row}, {col}) has viable escape route - not pocketed")
                return False

        # All other cases (2+ open sides) are not considered pockets
        print(f"    ✅ Cell ({row}, {col}) has {len(open_sides)} open sides - not pocketed")
        return False

    def _would_be_dead_end(self, row, col, path_positions):
        """Check if a cell would be a dead end (surrounded on 3+ sides)."""
        if not (0 <= row < self.rows and 0 <= col < self.cols):
            return True

        blocked_count = 0
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]

        for dr, dc in directions:
            neighbor_row, neighbor_col = row + dr, col + dc

            if (not (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols) or
                (neighbor_row, neighbor_col) in path_positions or
                (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols and
                 self.grid[neighbor_row][neighbor_col] != 0)):
                blocked_count += 1

        return blocked_count >= 3

    def _is_problematic_opposite_opening(self, row, col, path, open_sides, directions):
        """
        Check if a cell with opposite open sides creates a problematic pocket.
        This handles cases where a cell has openings on opposite sides but is still problematic.
        """
        # For now, be conservative and don't flag opposite openings as pockets
        # This handles the case where a path runs along an edge and a cell has
        # openings to the north/south or east/west
        return False

    def _is_cell_surrounded_by_path(self, row, col, path):
        """Check if a cell is surrounded on 3+ sides by the current path."""
        path_positions = set(path)
        surrounded_sides = 0

        # Check all 4 directions around the cell
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        for dr, dc in directions:
            neighbor_row, neighbor_col = row + dr, col + dc

            # Count as surrounded if:
            # 1. The neighbor is part of our current path, OR
            # 2. The neighbor is out of bounds (edge of grid), OR
            # 3. The neighbor is occupied by another symbol
            if ((neighbor_row, neighbor_col) in path_positions or
                not (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols) or
                (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols and
                 self.grid[neighbor_row][neighbor_col] != 0 and
                 (neighbor_row, neighbor_col) not in path_positions)):
                surrounded_sides += 1

        # Consider it surrounded if 3 or more sides are blocked
        # This catches C-shapes and other concave patterns
        return surrounded_sides >= 3

    def _check_for_extended_c_patterns(self, path):
        """Check for extended C-patterns that create multiple trapped cells."""
        if len(path) < 6:
            return False

        # Look for patterns where we have 2+ consecutive empty cells being surrounded
        # This catches cases like:
        # X X X
        # X . .  <- two empty cells in a row being surrounded
        # X X X

        path_positions = set(path)

        # Check for horizontal lines of trapped cells
        for row in range(self.rows):
            consecutive_trapped = 0
            for col in range(self.cols):
                if (self.grid[row][col] == 0 and
                    (row, col) not in path_positions and
                    self._is_cell_surrounded_by_path(row, col, path)):
                    consecutive_trapped += 1
                    if consecutive_trapped >= 2:
                        print(f"    ❌ Found {consecutive_trapped} consecutive trapped cells in row {row}")
                        return True
                else:
                    consecutive_trapped = 0

        # Check for vertical lines of trapped cells
        for col in range(self.cols):
            consecutive_trapped = 0
            for row in range(self.rows):
                if (self.grid[row][col] == 0 and
                    (row, col) not in path_positions and
                    self._is_cell_surrounded_by_path(row, col, path)):
                    consecutive_trapped += 1
                    if consecutive_trapped >= 2:
                        print(f"    ❌ Found {consecutive_trapped} consecutive trapped cells in column {col}")
                        return True
                else:
                    consecutive_trapped = 0

        return False

    def check_reachability(self):
        """
        Check if all empty cells can be reached by the remaining unsolved symbols.
        This prevents accepting paths that create unreachable areas.

        Returns:
            True if all empty cells can be reached, False otherwise
        """
        # Get all empty cells
        empty_cells = []
        for row in range(self.rows):
            for col in range(self.cols):
                if self.grid[row][col] == 0:
                    empty_cells.append((row, col))

        if not empty_cells:
            return True  # No empty cells to check

        # Get unsolved symbols (symbols without paths)
        unsolved_symbols = [symbol for symbol in self.pairs.keys() if symbol not in self.paths]

        if not unsolved_symbols:
            # All symbols are solved, but we have empty cells - this is invalid
            print(f"  ❌ Reachability check failed: All symbols solved but {len(empty_cells)} empty cells remain")
            return False

        # For each unsolved symbol, check if it can reach all empty cells
        # We'll use a flood fill approach from each symbol's endpoints
        reachable_cells = set()

        for symbol in unsolved_symbols:
            start, end = self.pairs[symbol]

            # Flood fill from start endpoint
            reachable_from_start = self._flood_fill_reachable(start, empty_cells)
            reachable_cells.update(reachable_from_start)

            # Flood fill from end endpoint
            reachable_from_end = self._flood_fill_reachable(end, empty_cells)
            reachable_cells.update(reachable_from_end)

        # Check if all empty cells are reachable
        empty_cells_set = set(empty_cells)
        unreachable_cells = empty_cells_set - reachable_cells

        if unreachable_cells:
            print(f"  ❌ Reachability check failed: {len(unreachable_cells)} cells unreachable: {list(unreachable_cells)}")
            return False

        print(f"  ✅ Reachability check passed: All {len(empty_cells)} empty cells are reachable")
        return True

    def _flood_fill_reachable(self, start_pos, target_cells):
        """
        Perform flood fill to find all cells reachable from start_pos.
        Only considers empty cells and target_cells as reachable.

        Args:
            start_pos: Starting position (row, col)
            target_cells: List of cells we're trying to reach

        Returns:
            Set of reachable cells from target_cells
        """
        visited = set()
        queue = [start_pos]
        reachable = set()
        target_set = set(target_cells)

        while queue:
            current = queue.pop(0)
            if current in visited:
                continue

            visited.add(current)

            # If this is one of our target empty cells, mark it as reachable
            if current in target_set:
                reachable.add(current)

            # Explore neighbors
            for neighbor in self.get_neighbors(current[0], current[1]):
                if neighbor in visited:
                    continue

                # Can move to empty cells or target cells
                neighbor_row, neighbor_col = neighbor
                if (self.grid[neighbor_row][neighbor_col] == 0 or
                    neighbor in target_set):
                    queue.append(neighbor)

        return reachable

    def find_path(self, symbol, start, end, path, visited):
        """
        Find a path from start to end for the given symbol using backtracking.

        Args:
            symbol: The symbol/number we're connecting
            start: Starting coordinates (row, col)
            end: Ending coordinates (row, col)
            path: Current path being built
            visited: Set of visited coordinates for this path

        Returns:
            List of coordinates representing the path, or None if no path found
        """
        row, col = start

        # If we reached the end, return the path
        if start == end:
            return path

        # Try each neighbor
        neighbors = self.get_neighbors(row, col)
        print(f"  📍 Symbol {symbol}: Exploring from {start}, neighbors: {neighbors}")

        # OPTIMIZATION: If we're adjacent to the endpoint, move directly there
        if end in neighbors:
            next_row, next_col = end

            # Make the move to the endpoint
            visited.add((next_row, next_col))
            new_path = path + [(next_row, next_col)]

            # Show board state after making the move to endpoint
            step_num = len(new_path) - 1
            self.print_current_grid(f"Symbol {symbol} Step {step_num}: Moving to endpoint {end}")

            # Since this is the endpoint, we've found our path
            return new_path

        for next_row, next_col in neighbors:
            # Skip if already visited in this path
            if (next_row, next_col) in visited:
                print(f"    ⏭️  Skipping {(next_row, next_col)} - already visited")
                continue

            # Skip if this move is not valid
            if not self.is_valid_move(next_row, next_col, symbol):
                print(f"    ⏭️  Skipping {(next_row, next_col)} - invalid move")
                continue

            # If this is not the target endpoint and the cell is occupied, skip
            if (next_row, next_col) != end and self.grid[next_row][next_col] != 0:
                print(f"    ⏭️  Skipping {(next_row, next_col)} - cell occupied")
                continue

            print(f"\n  🎯 Symbol {symbol}: Trying move to ({next_row}, {next_col})")

            # Make the move
            old_value = self.grid[next_row][next_col]
            if (next_row, next_col) != end:  # Don't overwrite the endpoint
                self.grid[next_row][next_col] = symbol

            visited.add((next_row, next_col))
            new_path = path + [(next_row, next_col)]
            self.print_grid_with_path(new_path, symbol, (next_row, next_col))

            # Check if this move creates a bad shape
            if self.has_bad_shape(new_path, symbol):
                print(f"  ⬅️  Symbol {symbol}: Move to ({next_row}, {next_col}) creates a bad shape, backtracking")
                visited.remove((next_row, next_col))
                if (next_row, next_col) != end:
                    self.grid[next_row][next_col] = old_value
                print()  # Add line break after backtracking
                continue

            # Recursively try to complete the path
            result = self.find_path(symbol, (next_row, next_col), end, new_path, visited)

            if result is not None:
                return result

            # Backtrack
            print(f"\n  ⬅️  Symbol {symbol}: Backtracking from ({next_row}, {next_col})")
            visited.remove((next_row, next_col))
            if (next_row, next_col) != end:
                self.grid[next_row][next_col] = old_value
            print()  # Add line break after backtracking

        return None

    def find_all_paths(self, symbol, start, end, max_paths=10):
        """
        Find all valid paths for a symbol, up to max_paths.

        Args:
            symbol: The symbol to find paths for
            start: Starting coordinates
            end: Ending coordinates
            max_paths: Maximum number of paths to find

        Returns:
            List of dictionaries with 'path' and 'quality' keys
        """
        print(f"\n🔍 Finding all paths for symbol {symbol}: {start} → {end}")

        all_paths = []
        self._find_all_paths_recursive(symbol, start, end, [start], {start}, all_paths, max_paths)

        # Calculate quality for each path
        for path_info in all_paths:
            path_info['quality'] = self.calculate_path_quality(path_info['path'], symbol)

        # Sort by quality (higher is better, but all are 0 for now)
        all_paths.sort(key=lambda x: x['quality'], reverse=True)

        print(f"  📊 Found {len(all_paths)} valid paths for symbol {symbol}")
        for i, path_info in enumerate(all_paths):
            print(f"    Path {i+1}: {len(path_info['path'])} steps, quality: {path_info['quality']}")

        return all_paths

    def _find_all_paths_recursive(self, symbol, start, end, path, visited, all_paths, max_paths):
        """
        Recursive helper to find all valid paths.
        """
        if len(all_paths) >= max_paths:
            return  # Stop if we've found enough paths

        row, col = start

        # If we reached the end, store this path
        if start == end:
            # Check if this path creates unreachable areas
            if self._is_path_valid_for_reachability(path, symbol):
                all_paths.append({'path': path[:], 'quality': 0})
            return

        # Try each neighbor
        neighbors = self.get_neighbors(row, col)

        # OPTIMIZATION: If we're adjacent to the endpoint, move directly there
        if end in neighbors:
            next_row, next_col = end
            visited.add((next_row, next_col))
            new_path = path + [(next_row, next_col)]

            # Check if this path is valid
            if self._is_path_valid_for_reachability(new_path, symbol):
                all_paths.append({'path': new_path[:], 'quality': 0})

            visited.remove((next_row, next_col))
            return

        for next_row, next_col in neighbors:
            # Skip if already visited in this path
            if (next_row, next_col) in visited:
                continue

            # Skip if this move is not valid
            if not self.is_valid_move(next_row, next_col, symbol):
                continue

            # If this is not the target endpoint and the cell is occupied, skip
            if (next_row, next_col) != end and self.grid[next_row][next_col] != 0:
                continue

            # Make the move temporarily
            old_value = self.grid[next_row][next_col]
            if (next_row, next_col) != end:
                self.grid[next_row][next_col] = symbol

            visited.add((next_row, next_col))
            new_path = path + [(next_row, next_col)]

            # Check if this move creates a bad shape
            if not self.has_bad_shape(new_path, symbol):
                # Recursively continue building paths
                self._find_all_paths_recursive(symbol, (next_row, next_col), end, new_path, visited, all_paths, max_paths)

            # Backtrack
            visited.remove((next_row, next_col))
            if (next_row, next_col) != end:
                self.grid[next_row][next_col] = old_value

    def _is_path_valid_for_reachability(self, path, symbol):
        """
        Check if a path would leave the board in a valid state for reachability.
        This is a simplified check that doesn't require the full grid state.
        """
        # For now, just return True - we'll do full reachability check later
        # This could be enhanced to do more sophisticated checking
        return True

    def calculate_path_quality(self, path, symbol):
        """
        Calculate a quality metric for a path.
        Higher values indicate better paths.

        For now, all paths get quality 0 as requested.
        Future enhancements could consider:
        - Path length (shorter might be better)
        - How much space it leaves for other paths
        - Edge preference
        - Avoiding creating bottlenecks

        Args:
            path: List of coordinates representing the path
            symbol: The symbol this path is for

        Returns:
            Quality score (currently always 0)
        """
        # All paths get quality 0 for now as requested
        return 0

    def solve(self):
        """
        Solve the Flow Free puzzle using enhanced backtracking with multiple paths.

        Returns:
            True if solved successfully, False otherwise
        """
        symbols = list(self.pairs.keys())

        # First, find all valid paths for each symbol
        print("\n🔍 Phase 1: Finding all valid paths for each symbol...")
        for symbol in symbols:
            start, end = self.pairs[symbol]
            paths = self.find_all_paths(symbol, start, end)
            self.all_paths[symbol] = paths

            if not paths:
                print(f"❌ No valid paths found for symbol {symbol}")
                return False

        # Now try to solve using combinations of these paths
        print("\n🔍 Phase 2: Finding compatible path combinations...")
        return self.solve_with_multiple_paths(symbols, 0)

    def solve_with_multiple_paths(self, symbols, index):
        """
        Enhanced recursive solver that tries multiple path combinations.
        """
        # Base case: all pairs connected
        if index == len(symbols):
            # Check if the solution is complete and valid
            if self.is_complete() and self.check_reachability():
                return True
            return False

        symbol = symbols[index]
        start, end = self.pairs[symbol]

        print(f"\n🔍 Trying paths for symbol {symbol}: {start} → {end}")

        # Try each available path for this symbol
        available_paths = self.all_paths[symbol]

        for i, path_info in enumerate(available_paths):
            path = path_info['path']
            quality = path_info['quality']

            print(f"  🎯 Trying path {i+1}/{len(available_paths)} (quality: {quality})")

            # Check if this path conflicts with existing paths
            if self._path_conflicts_with_existing(path, symbol):
                print(f"    ⏭️  Path conflicts with existing paths, skipping")
                continue

            # Apply the path
            self._apply_path(path, symbol)
            self.paths[symbol] = path

            # Check reachability after applying this path
            if not self.check_reachability():
                print(f"    ❌ Path creates unreachable areas, backtracking")
                self._remove_path(path, symbol)
                del self.paths[symbol]
                continue

            # Try to solve the rest
            if self.solve_with_multiple_paths(symbols, index + 1):
                return True

            # Backtrack: remove this path
            print(f"    ⬅️  Backtracking: removing path for symbol {symbol}")
            self._remove_path(path, symbol)
            del self.paths[symbol]

        print(f"❌ No valid path combination found for symbol {symbol}")
        return False

    def _path_conflicts_with_existing(self, path, symbol):
        """
        Check if a path conflicts with already placed paths.

        Args:
            path: List of coordinates for the new path
            symbol: Symbol for the new path

        Returns:
            True if there's a conflict, False otherwise
        """
        # Get all currently occupied cells (excluding endpoints)
        occupied_cells = set()
        for existing_symbol, existing_path in self.paths.items():
            if existing_symbol != symbol:
                # Add all path cells except endpoints
                occupied_cells.update(existing_path[1:-1])

        # Check if any part of the new path (except endpoints) conflicts
        path_cells = set(path[1:-1])  # Exclude endpoints
        conflicts = path_cells.intersection(occupied_cells)

        return len(conflicts) > 0

    def _apply_path(self, path, symbol):
        """
        Apply a path to the grid.

        Args:
            path: List of coordinates representing the path
            symbol: Symbol to place on the path
        """
        for row, col in path[1:-1]:  # Don't overwrite endpoints
            self.grid[row][col] = symbol

    def _remove_path(self, path, symbol):
        """
        Remove a path from the grid.

        Args:
            path: List of coordinates representing the path
            symbol: Symbol to remove from the path
        """
        for row, col in path[1:-1]:  # Don't remove endpoints
            self.grid[row][col] = 0

    def solve_recursive(self, symbols, index):
        """
        Legacy recursive helper for solving the puzzle.
        This method is kept for backward compatibility but now includes reachability checking.
        """
        # Base case: all pairs connected
        if index == len(symbols):
            return self.is_complete() and self.check_reachability()

        symbol = symbols[index]
        start, end = self.pairs[symbol]

        print(f"\n🔍 Solving symbol {symbol}: {start} → {end}")

        # Try to find a path for this symbol
        path = self.find_path(symbol, start, end, [start], {start})

        if path is not None:
            print(f"✅ Found path for symbol {symbol}")
            # Store the path and check reachability
            self.paths[symbol] = path

            # Check if this path leaves the board in a valid state
            if self.check_reachability():
                if self.solve_recursive(symbols, index + 1):
                    return True
            else:
                print(f"  ❌ Path creates unreachable areas")

            # Backtrack: remove this path
            print(f"⬅️  Backtracking: removing path for symbol {symbol}")
            del self.paths[symbol]
            for row, col in path[1:-1]:  # Don't remove endpoints
                self.grid[row][col] = 0

        else:
            print(f"❌ No path found for symbol {symbol}")

        return False

    def is_complete(self):
        """Check if all cells in the grid are filled."""
        for row in range(self.rows):
            for col in range(self.cols):
                if self.grid[row][col] == 0:
                    return False
        return True

    def print_solution(self):
        """Print the solved grid and paths."""
        print("\n" + "="*50)
        print("🎉 SOLUTION FOUND!")
        print("="*50)
        self._print_formatted_grid(self.grid, "Final Solution")

        print("\n📍 Solution Paths:")
        print("-" * 30)
        for symbol, path in self.paths.items():
            start, end = self.pairs[symbol]
            print(f"  Symbol {symbol}: {start} → {end}")
            print(f"    Path: {' → '.join(map(str, path))}")

            # Show quality if available
            if symbol in self.all_paths:
                for path_info in self.all_paths[symbol]:
                    if path_info['path'] == path:
                        print(f"    Quality: {path_info['quality']}")
                        break
        print()

    def print_all_paths_info(self):
        """Print information about all discovered paths for each symbol."""
        print("\n" + "="*60)
        print("📊 ALL DISCOVERED PATHS")
        print("="*60)

        for symbol in self.pairs.keys():
            start, end = self.pairs[symbol]
            print(f"\nSymbol {symbol}: {start} → {end}")

            if symbol in self.all_paths and self.all_paths[symbol]:
                print(f"  Found {len(self.all_paths[symbol])} valid paths:")

                for i, path_info in enumerate(self.all_paths[symbol]):
                    path = path_info['path']
                    quality = path_info['quality']
                    length = len(path)

                    # Mark if this is the currently used path
                    is_current = symbol in self.paths and self.paths[symbol] == path
                    marker = " ⭐ USED" if is_current else ""

                    print(f"    Path {i+1}: {length} steps, quality: {quality}{marker}")
                    print(f"      Route: {' → '.join(map(str, path))}")
            else:
                print("  No valid paths found")
        print()

    def get_solution_grid(self):
        """Return the solution grid as a 2D list."""
        return [row[:] for row in self.grid]  # Return a copy

    def print_current_grid(self, title="Current Grid State"):
        """Print the current state of the grid for debugging."""
        self._print_formatted_grid(self.grid, title)

    def print_grid_with_path(self, path, symbol, current_pos=None):
        """Print the current grid with the current path highlighted."""
        temp_grid = [row[:] for row in self.grid]

        # Mark the path with a special character
        for r, c in path:
            if temp_grid[r][c] == 0:  # Don't overwrite endpoints
                temp_grid[r][c] = symbol  # Mark path with symbol

        title = f"Exploring Symbol {symbol} → {current_pos if current_pos else 'path'}"
        self._print_formatted_grid_with_highlight(temp_grid, title, current_pos, symbol)

    def _print_formatted_grid(self, grid, title="Grid", compact=False):
        """Print a nicely formatted grid with borders and proper alignment."""
        if not compact:
            print(f"\n┌─ {title} ─" + "─" * max(0, 40 - len(title)) + "┐")
        else:
            print(f"  {title}:")

        # Calculate the width needed for each cell
        max_width = 3
        for row in grid:
            for cell in row:
                cell_str = str(cell) if cell != 0 else '·'
                max_width = max(max_width, len(cell_str))

        # Print the grid with proper formatting
        for i, row in enumerate(grid):
            if not compact:
                line = "│ "
            else:
                line = "    "

            for j, cell in enumerate(row):
                cell_str = str(cell) if cell != 0 else '·'
                line += f"{cell_str:^{max_width}}"
                if j < len(row) - 1:
                    line += " "

            if not compact:
                line += " │"
            print(line)

        if not compact:
            print("└" + "─" * (len(line) - 1) + "┘")
        print()

    def _print_formatted_grid_with_highlight(self, grid, title="Grid", highlight_pos=None, highlight_symbol=None):
        """Print a nicely formatted grid with a highlighted position marked with asterisk."""
        print(f"  {title}:")

        # Calculate the width needed for each cell (accounting for asterisk)
        max_width = 3
        for row in grid:
            for cell in row:
                cell_str = str(cell) if cell != 0 else '·'
                max_width = max(max_width, len(cell_str) + 1)  # +1 for potential asterisk

        # Print the grid with proper formatting
        for i, row in enumerate(grid):
            line = "    "

            for j, cell in enumerate(row):
                cell_str = str(cell) if cell != 0 else '·'

                # Add asterisk if this is the highlighted position
                if highlight_pos and (i, j) == highlight_pos:
                    cell_str += "*"

                line += f"{cell_str:^{max_width}}"
                if j < len(row) - 1:
                    line += " "

            print(line)
        print()


def solve_flow_free(rows, cols, pairs):
    """
    Convenience function to solve a Flow Free puzzle.

    Args:
        rows (int): Number of rows
        cols (int): Number of columns
        pairs (dict): Dictionary of symbol -> ((start_row, start_col), (end_row, end_col))

    Returns:
        2D list representing the solved grid, or None if no solution exists
    """
    solver = FlowFreeSolver(rows, cols, pairs)

    if solver.solve():
        return solver.get_solution_grid()
    else:
        return None


# Example usage
if __name__ == "__main__":
    print("FlowFreeSolver - Use test files to run specific puzzles")
    print("Available test files:")
    print("  - test_simple.py: Simple 1x3 puzzle")
    print("  - test_5x5.py: Complex 5x5 puzzle")
    print("  - test_custom.py: Template for custom puzzles")