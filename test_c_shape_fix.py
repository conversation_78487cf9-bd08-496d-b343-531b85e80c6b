#!/usr/bin/env python3
"""
Test the fixed C-shape detection logic to ensure it properly detects
C-shapes with two turns rather than just L-shapes with one turn.
"""

from main import FlowFreeSolver

def test_c_shape_detection_fix():
    """Test that the fixed C-shape detection properly identifies C-shapes vs L-shapes."""
    print("=== Testing Fixed C-Shape Detection ===")
    
    # Create a solver for testing
    solver = FlowFreeSolver(5, 5, {1: ((0, 0), (4, 4))})
    
    # Test 1: True C-shape pattern (should be detected)
    # Pattern: horizontal → vertical → horizontal (opposite direction)
    # (0,0) → (0,1) → (1,1) → (1,0) → (1,-1) would be a C-shape
    # But since we can't have negative coordinates, let's use:
    # (2,2) → (2,3) → (3,3) → (3,2) → (3,1)
    true_c_shape = [(2, 2), (2, 3), (3, 3), (3, 2), (3, 1)]
    print(f"Testing true C-shape: {true_c_shape}")
    result1 = solver._is_partial_c_shape(true_c_shape)
    print(f"Result: {result1} (should be True)")
    
    # Test 2: L-shape pattern (should NOT be detected as C-shape)
    # Pattern: horizontal → vertical (only one turn)
    # (0,0) → (0,1) → (0,2) → (1,2) → (2,2)
    l_shape = [(0, 0), (0, 1), (0, 2), (1, 2), (2, 2)]
    print(f"\nTesting L-shape: {l_shape}")
    result2 = solver._is_partial_c_shape(l_shape)
    print(f"Result: {result2} (should be False)")
    
    # Test 3: Another true C-shape pattern (vertical start)
    # Pattern: vertical → horizontal → vertical (opposite direction)
    # (2,2) → (3,2) → (3,3) → (2,3) → (1,3)
    true_c_shape_vertical = [(2, 2), (3, 2), (3, 3), (2, 3), (1, 3)]
    print(f"\nTesting true C-shape (vertical start): {true_c_shape_vertical}")
    result3 = solver._is_partial_c_shape(true_c_shape_vertical)
    print(f"Result: {result3} (should be True)")
    
    # Test 4: Straight line (should NOT be detected)
    straight_line = [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4)]
    print(f"\nTesting straight line: {straight_line}")
    result4 = solver._is_partial_c_shape(straight_line)
    print(f"Result: {result4} (should be False)")
    
    # Test 5: U-shape that goes back in same direction (should NOT be detected)
    # Pattern: horizontal → vertical → horizontal (same direction as initial)
    u_shape = [(2, 2), (2, 3), (3, 3), (3, 4), (3, 5)]
    print(f"\nTesting U-shape (same direction): {u_shape}")
    result5 = solver._is_partial_c_shape(u_shape)
    print(f"Result: {result5} (should be False)")
    
    # Summary
    print(f"\n=== Test Results Summary ===")
    print(f"True C-shape (horizontal): {result1} (expected: True)")
    print(f"L-shape: {result2} (expected: False)")
    print(f"True C-shape (vertical): {result3} (expected: True)")
    print(f"Straight line: {result4} (expected: False)")
    print(f"U-shape (same direction): {result5} (expected: False)")
    
    # Check if all tests passed
    expected_results = [True, False, True, False, False]
    actual_results = [result1, result2, result3, result4, result5]
    
    if actual_results == expected_results:
        print("\n✅ All tests passed! C-shape detection is working correctly.")
        return True
    else:
        print(f"\n❌ Some tests failed!")
        print(f"Expected: {expected_results}")
        print(f"Actual:   {actual_results}")
        return False

def test_integration_with_solver():
    """Test that the fixed C-shape detection works in the context of the full solver."""
    print("\n=== Integration Test with Solver ===")
    
    # Create a puzzle that could potentially create C-shapes
    pairs = {
        1: ((0, 0), (2, 4))  # This path might try to create C-shapes
    }
    
    solver = FlowFreeSolver(3, 5, pairs)
    solver.print_current_grid("Integration Test Puzzle")
    
    print("🚀 Starting solver with fixed C-shape detection...")
    if solver.solve():
        print("✅ Puzzle solved successfully!")
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (this might be expected if C-shape detection is working)")
        solver.print_current_grid("Final State")
        return False

if __name__ == "__main__":
    print("Testing the fixed C-shape detection logic...\n")
    
    # Run the tests
    test1_passed = test_c_shape_detection_fix()
    test2_passed = test_integration_with_solver()
    
    print(f"\n=== Final Results ===")
    print(f"C-shape detection tests: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Integration test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
